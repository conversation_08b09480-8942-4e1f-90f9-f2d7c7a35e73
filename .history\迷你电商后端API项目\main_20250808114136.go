package main

import (
	"fmt"
	"log"
	"log/slog"
	"net/http"
	"os"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// 仓库订单消息通道channel
var wareHouseOrderChan = make(chan Order, 100)

// 配置结构体
// `mapstructure`标签是Viper用来将配置文件中的键映射到`struct`字段的。
type Config struct {
	Server struct {
		Port string `mapstructure:"port"`
	} `mapstructure:"server"`
	Database struct {
		DSN string `mapstructure:"dsn"`
	} `mapstructure:"database"`
}

// 构建数据模型
// 用户数据
type User struct {
	ID    uint   `json:"id"`
	Name  string `json:"name"`
	Email string `json:"email" gorm:"unique"`
}

// 商品数据
type Product struct {
	ID    uint    `json:"id"`    //商品ID
	Name  string  `json:"name"`  //商品名称
	Price float64 `json:"price"` //价格
	Stock uint    `json:"stock"` //库存
}

// 订单项数据
type OrderItem struct {
	ID        uint    `json:"id" gorm:"primaryKey"` //订单项ID，主键
	OrderID   uint    `json:"order_id"`             //订单ID，外键
	ProductID uint    `json:"product_id"`           //商品ID
	Quantity  uint    `json:"quantity"`             //数量
	Price     float64 `json:"price"`                //价格
}

// 订单数据
type Order struct {
	ID          uint        `json:"id" gorm:"primaryKey"`            //订单ID，主键
	UserID      uint        `json:"user_id"`                         //用户ID，外键 与User表建立连接
	Items       []OrderItem `json:"items" gorm:"foreignKey:OrderID"` //订单项，一对多关系
	TotalAmount float64     `json:"total_amount"`                    //订单总金额
	CreatedAt   time.Time   `json:"created_at"`                      //创建时间
}

// 自定义日志格式
func Newlogger() *slog.Logger {
	// 创建一个JSON格式的处理器，将日志写入标准输出
	handler := slog.NewJSONHandler(os.Stdout, nil)
	// 创建一个新的logger实例
	logger := slog.New(handler)
	return logger
}

// 自定义错误
type ResourceError struct {
	StatusCode int    `json:"status"`
	Field      string `json:"field"`
	Message    string `json:"message"`
}

// 实现error接口的Error方法
func (e *ResourceError) Error() string {
	return fmt.Sprintf("错误码: %d, 错误信息: %s %s", e.StatusCode, e.Field, e.Message)
}

func NotFound(c *gin.Context, field string) {
	err := &ResourceError{
		StatusCode: http.StatusNotFound,
		Field:      field,
		Message:    "资源未找到",
	}
	c.JSON(err.StatusCode, gin.H{"error": err})
}

// 加载配置文件
func loadConfig() (config Config, err error) {
	viper.SetConfigName("config") // 配置文件名 (不带后缀)
	viper.AddConfigPath(".")      //配置文件路径（当前目录下）
	viper.SetConfigType("yaml")   //配置文件格式(yaml)
	err = viper.ReadInConfig()    //读取配置
	if err != nil {
		return
	}
	err = viper.Unmarshal(&config) //将配置解析到Config结构体中
	return
}

// 初始化数据库
func initGormDB(filePath string) (*gorm.DB, error) {
	db, err := gorm.Open(sqlite.Open(filePath), &gorm.Config{})
	if err != nil {
		return nil, err
	}
	// 自动迁移,不存在则创建表，存在则更新表结构
	err = db.AutoMigrate(&User{}, &Product{}, &Order{}, &OrderItem{})
	if err != nil {
		return nil, err
	}
	return db, nil
}

// 服务能力
type Server struct {
	db             *gorm.DB
	logger         *slog.Logger
	productMutexes map[uint]*sync.Mutex // 每个商品的独立锁
	mapMutex       sync.RWMutex         // 保护productMutexes映射的读写锁
}

// 获取指定商品的互斥锁，如果不存在则创建
func (s *Server) getProductMutex(productID uint) *sync.Mutex {
	// 先尝试读锁获取
	s.mapMutex.RLock()
	// 检查商品锁是否存在
	if mutex, exists := s.productMutexes[productID]; exists {
		// 释放读锁
		s.mapMutex.RUnlock()
		return mutex
	}
	s.mapMutex.RUnlock()

	// 需要创建新的锁，使用写锁
	s.mapMutex.Lock()
	defer s.mapMutex.Unlock()

	// 双重检查，防止并发创建
	if mutex, exists := s.productMutexes[productID]; exists {
		return mutex
	}

	// 创建新的互斥锁
	mutex := &sync.Mutex{}
	s.productMutexes[productID] = mutex
	return mutex
}

// 发送订单信息到仓库进行打包
func sendOrderToWarehouse(order Order) {
	log.Printf("新的订单已创建，订单号: %d,", order.ID)
	// 模拟处理时间
	time.Sleep(3 * time.Second)
	log.Printf("仓库已收到订单 %d,待打包发货", order.ID)
}

// 仓库订单服务
func (s *Server) wareHouseOrderServer(wareHouseOrderChan <-chan Order) {
	s.logger.Info("仓库订单服务已启动")
	for order := range wareHouseOrderChan {
		sendOrderToWarehouse(order)
	}
	s.logger.Info("仓库订单服务已关闭")
}

// 创建用户
func (s *Server) createUserHandler(c *gin.Context) {
	var newUser User
	//c.ShouldBindJSON 会将请求的JSON正文解析并填充到 user 结构体中
	err := c.ShouldBindJSON(&newUser)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid json input"})
		return
	}
	//db.Create 创建插入值，返回插入的数据的值ID中的数据的主键
	result := s.db.Create(&newUser)
	if result.Error != nil {
		s.logger.Error("创建用户失败", "error", result.Error)
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	s.logger.Info("创建用户成功", "user", newUser)
	c.JSON(http.StatusCreated, newUser)
}

// 删除用户
func (s *Server) deleteUserHandler(c *gin.Context) {
	//先判断用户存不存在
	var user User
	id := c.Params.ByName("id")
	result := s.db.First(&user, id)
	if result.Error == gorm.ErrRecordNotFound {
		s.logger.Error("未找到用户", "error", result.Error)
		NotFound(c, "user")
		return
	} else if result.Error != nil {
		s.logger.Error("获取用户失败", "error", result.Error)
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	//删除用户
	result = s.db.Delete(&user)
	if result.Error != nil {
		s.logger.Error("删除用户失败", "error", result.Error)
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	s.logger.Info("删除用户成功", "user", user)
	c.JSON(http.StatusOK, gin.H{"message": "user deleted"})
}

// 获取所有用户
func (s *Server) getAllUserHandler(c *gin.Context) {
	var users []User
	//db.Find 查询所有数据
	result := s.db.Find(&users)
	if result.Error != nil {
		s.logger.Error("获取所有用户失败", "error", result.Error)
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	s.logger.Info("获取所有用户成功", "users", users)
	c.JSON(http.StatusOK, users)
}

// 获取单一用户
func (s *Server) getUserHandler(c *gin.Context) {
	var user User
	id := c.Params.ByName("id")
	result := s.db.First(&user, id)
	if result.Error == gorm.ErrRecordNotFound {
		s.logger.Error("未找到用户", "error", result.Error)
		NotFound(c, "user")
		return
	} else if result.Error != nil {
		s.logger.Error("获取用户失败", "error", result.Error)
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	s.logger.Info("获取用户成功", "user", user)
	c.JSON(http.StatusOK, user)
}

// 添加/上架商品
func (s *Server) createProductHandler(c *gin.Context) {
	var newProduct Product
	//c.ShouldBindJSON 会将请求的JSON正文解析并填充到 product 结构体中
	err := c.ShouldBindJSON(&newProduct)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid json input"})
		return
	}
	//db.Create 创建插入值，返回插入的数据的值ID中的数据的主键
	result := s.db.Create(&newProduct)
	if result.Error != nil {
		s.logger.Error("创建商品失败", "error", result.Error)
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	s.logger.Info("创建商品成功", "product", newProduct)
	c.JSON(http.StatusCreated, newProduct)
}

// 修改商品信息
func (s *Server) updateProductHandler(c *gin.Context) {
	//先判断商品存不存在
	var product Product
	id := c.Params.ByName("id")
	result := s.db.First(&product, id)
	if result.Error == gorm.ErrRecordNotFound {
		s.logger.Error("未找到商品", "error", result.Error)
		NotFound(c, "product")
		return
	} else if result.Error != nil {
		s.logger.Error("获取商品失败", "error", result.Error)
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	//修改商品信息
	var updatedProduct Product
	err := c.ShouldBindJSON(&updatedProduct)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid json input"})
		return
	}
	//更新商品
	result = s.db.Model(&product).Updates(updatedProduct)
	if result.Error != nil {
		s.logger.Error("更新商品失败", "error", result.Error)
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	s.logger.Info("更新商品成功", "product", product)
	c.JSON(http.StatusOK, product)
}

// 删除/下架商品
func (s *Server) deleteProductHandler(c *gin.Context) {
	//先判断商品存不存在
	var product Product
	id := c.Params.ByName("id")
	result := s.db.First(&product, id)
	if result.Error == gorm.ErrRecordNotFound {
		s.logger.Error("未找到商品", "error", result.Error)
		NotFound(c, "product")
		return
	} else if result.Error != nil {
		s.logger.Error("获取商品失败", "error", result.Error)
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	//删除商品
	result = s.db.Delete(&product)
	if result.Error != nil {
		s.logger.Error("删除商品失败", "error", result.Error)
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	s.logger.Info("删除商品成功", "product", product)
	c.JSON(http.StatusOK, gin.H{"message": "product deleted"})
}

// 获取所有商品
func (s *Server) getAllProductsHandler(c *gin.Context) {
	var products []Product
	result := s.db.Find(&products)
	if result.Error != nil {
		s.logger.Error("获取所有商品失败", "error", result.Error)
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	s.logger.Info("获取所有商品成功", "products", products)
	c.JSON(http.StatusOK, products)
}

// 根据id获取单一商品
func (s *Server) getProductHandler(c *gin.Context) {
	//先判断商品存不存在
	var product Product
	id := c.Params.ByName("id")
	result := s.db.First(&product, id)
	if result.Error == gorm.ErrRecordNotFound {
		s.logger.Error("未找到商品", "error", result.Error)
		NotFound(c, "product")
		return
	} else if result.Error != nil {
		s.logger.Error("获取商品失败", "error", result.Error)
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	s.logger.Info("获取商品成功", "product", product)
	c.JSON(http.StatusOK, product)
}

// 下单/创建订单
func (s *Server) createOrderHandler(c *gin.Context) {
	var newOrder Order
	err := c.ShouldBindJSON(&newOrder)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid json input"})
		return
	}
	//校验用户是否存在
	var user User
	result := s.db.First(&user, newOrder.UserID)
	if result.Error == gorm.ErrRecordNotFound {
		s.logger.Error("未找到用户", "error", result.Error)
		NotFound(c, "user")
		return
	} else if result.Error != nil {
		s.logger.Error("获取用户失败", "error", result.Error)
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	// 使用事务处理订单创建
	//事务以块开始交易，返回错误将回滚，否则要提交。事务在交易中执行FC中的任意命令。成功进行了变化；如果发生错误，它们会退缩。
	err = s.db.Transaction(func(tx *gorm.DB) error {
		// 订单总金额
		var totalAmount float64 = 0

		//校验商品是否存在并计算价格
		var product Product
		for i, item := range newOrder.Items {
			// 获取该商品的专用锁
			productMutex := s.getProductMutex(item.ProductID)

			// 锁定该商品，保护"检查库存+扣减库存"的原子操作
			productMutex.Lock()

			//查询商品是否存在（在锁内重新查询确保数据最新）
			result := tx.First(&product, item.ProductID)
			if result.Error == gorm.ErrRecordNotFound {
				productMutex.Unlock() // 发生错误时释放锁
				s.logger.Error("未找到商品", "error", result.Error)
				return result.Error
			} else if result.Error != nil {
				productMutex.Unlock() // 发生错误时释放锁
				s.logger.Error("获取商品失败", "error", result.Error)
				return result.Error
			}

			//校验商品库存是否充足
			if product.Stock < item.Quantity {
				productMutex.Unlock() // 库存不足时释放锁
				s.logger.Error("商品库存不足", "error", "stock not enough")
				return fmt.Errorf("商品库存不足")
			}

			// 设置订单项的实际价格（从商品表获取）
			newOrder.Items[i].Price = product.Price
			// 计算该订单项的小计并累加到总金额
			itemTotal := product.Price * float64(item.Quantity)
			totalAmount += itemTotal

			//更新商品库存
			product.Stock -= item.Quantity
			if err := tx.Save(&product).Error; err != nil {
				productMutex.Unlock() // 保存失败时释放锁
				return err
			}

			// 成功完成该商品的库存操作，释放锁
			productMutex.Unlock()
		}

		// 设置订单总金额
		newOrder.TotalAmount = totalAmount
		// 设置创建时间
		newOrder.CreatedAt = time.Now()

		//创建订单
		return tx.Create(&newOrder).Error
	})

	if err != nil {
		s.logger.Error("创建订单失败", "error", err)
		if err.Error() == "商品库存不足" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "stock not enough"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	// 发送订单信息到仓库
	wareHouseOrderChan <- newOrder
	s.logger.Info("创建订单成功", "order", newOrder)
	c.JSON(http.StatusCreated, newOrder)
}

// 删除订单/取消订单
func (s *Server) deleteOrderHandler(c *gin.Context) {
	var order Order
	id := c.Params.ByName("id")
	result := s.db.First(&order, id)
	if result.Error == gorm.ErrRecordNotFound {
		s.logger.Error("未找到订单", "error", result.Error)
		NotFound(c, "order")
		return
	} else if result.Error != nil {
		s.logger.Error("获取订单失败", "error", result.Error)
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	//删除订单
	result = s.db.Delete(&order)
	if result.Error != nil {
		s.logger.Error("删除订单失败", "error", result.Error)
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	s.logger.Info("删除订单成功", "order", order)
	c.JSON(http.StatusOK, gin.H{"message": "order deleted"})
}

// 获取某个用户的所有订单历史
func (s *Server) getUserAllOrderHistoryHandler(c *gin.Context) {
	//先判断用户存不存在
	var user User
	id := c.Params.ByName("id")
	result := s.db.First(&user, id)
	if result.Error == gorm.ErrRecordNotFound {
		s.logger.Error("未找到用户", "error", result.Error)
		NotFound(c, "user")
		return
	} else if result.Error != nil {
		s.logger.Error("获取用户失败", "error", result.Error)
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	s.logger.Info("获取用户成功", "user", user)
	//获取用户所有订单
	var userOrders []Order
	result = s.db.Find(&userOrders, user.ID)
	if result.Error != nil {
		s.logger.Error("获取用户订单失败", "error", result.Error)
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	s.logger.Info("获取用户订单成功", "orders", userOrders)
	c.JSON(http.StatusOK, userOrders)
}

func main() {
	//加载配置文件
	config, err := loadConfig() //加载配置文件，返回配置结构体config和错误err
	if err != nil {
		fmt.Println("failed to load config")
		panic(err)
	}
	//初始化数据库
	db, err := initGormDB(config.Database.DSN) //初始化数据库，返回数据库连接对象db和错误err
	if err != nil {
		fmt.Println("failed to connect database")
		panic(err)
	}
	// 创建一个Server实例
	server := &Server{
		db:             db,
		logger:         Newlogger(),
		productMutexes: make(map[uint]*sync.Mutex),
	}
	// 创建一个 Gin 实例
	r := gin.Default()
	// 注册路由
	//用户路由组
	userRoutes := r.Group("/users")
	{
		userRoutes.POST("", server.createUserHandler)                       //创建用户路由
		userRoutes.GET("", server.getAllUserHandler)                        //获取所有用户路由
		userRoutes.GET("/:id", server.getUserHandler)                       //获取单一用户路由
		userRoutes.GET("/:id/orders", server.getUserAllOrderHistoryHandler) //获取单一用户的所有订单路由
		userRoutes.DELETE("/:id", server.deleteUserHandler)                 //删除用户路由
	}
	//商品路由组
	productRoutes := r.Group("/products")
	{
		productRoutes.POST("", server.createProductHandler)       //添加/上架商品路由
		productRoutes.GET("", server.getAllProductsHandler)       //获取所有商品路由
		productRoutes.GET("/:id", server.getProductHandler)       //获取单一商品路由
		productRoutes.PUT("/:id", server.updateProductHandler)    //修改商品信息路由
		productRoutes.DELETE("/:id", server.deleteProductHandler) //删除/下架商品路由
	}
	//订单路由组
	orderRoutes := r.Group("/orders")
	{
		orderRoutes.POST("", server.createOrderHandler)       //下单路由
		orderRoutes.DELETE("/:id", server.deleteOrderHandler) //删除订单路由
	}

	// 启动仓库订单服务
	go server.wareHouseOrderServer(wareHouseOrderChan)

	// 启动服务器
	r.Run(config.Server.Port)
}
