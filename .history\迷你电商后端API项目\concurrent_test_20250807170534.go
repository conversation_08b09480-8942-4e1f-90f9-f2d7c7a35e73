package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"
)

// 测试订单请求结构
type TestOrderRequest struct {
	UserID uint              `json:"user_id"`
	Items  []TestOrderItem   `json:"items"`
}

type TestOrderItem struct {
	ProductID uint `json:"product_id"`
	Quantity  uint `json:"quantity"`
}

// 测试结果统计
type TestResult struct {
	TotalRequests   int           // 总请求数
	SuccessCount    int           // 成功订单数
	FailureCount    int           // 失败订单数
	StockErrors     int           // 库存不足错误数
	OtherErrors     int           // 其他错误数
	AverageTime     time.Duration // 平均响应时间
	MaxTime         time.Duration // 最大响应时间
	MinTime         time.Duration // 最小响应时间
}

// 并发测试器
type ConcurrentTester struct {
	baseURL string
	client  *http.Client
	mutex   sync.Mutex
	result  TestResult
}

// 创建测试器
func NewConcurrentTester(baseURL string) *ConcurrentTester {
	return &ConcurrentTester{
		baseURL: baseURL,
		client: &http.Client{
			Timeout: 10 * time.Second,
		},
		result: TestResult{
			MinTime: time.Hour, // 初始化为很大的值
		},
	}
}

// 发送单个订单请求
func (ct *ConcurrentTester) sendOrderRequest(userID, productID, quantity uint) (bool, time.Duration, string) {
	startTime := time.Now()
	
	// 构造请求数据
	orderReq := TestOrderRequest{
		UserID: userID,
		Items: []TestOrderItem{
			{
				ProductID: productID,
				Quantity:  quantity,
			},
		},
	}
	
	jsonData, err := json.Marshal(orderReq)
	if err != nil {
		return false, time.Since(startTime), fmt.Sprintf("JSON编码错误: %v", err)
	}
	
	// 发送HTTP请求
	resp, err := ct.client.Post(
		ct.baseURL+"/orders",
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	if err != nil {
		return false, time.Since(startTime), fmt.Sprintf("网络请求错误: %v", err)
	}
	defer resp.Body.Close()
	
	duration := time.Since(startTime)
	
	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, duration, fmt.Sprintf("读取响应错误: %v", err)
	}
	
	// 判断请求是否成功
	if resp.StatusCode == http.StatusCreated {
		return true, duration, "订单创建成功"
	} else {
		return false, duration, fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body))
	}
}

// 更新测试结果统计
func (ct *ConcurrentTester) updateResult(success bool, duration time.Duration, errorMsg string) {
	ct.mutex.Lock()
	defer ct.mutex.Unlock()
	
	ct.result.TotalRequests++
	
	if success {
		ct.result.SuccessCount++
	} else {
		ct.result.FailureCount++
		// 分类错误类型
		if bytes.Contains([]byte(errorMsg), []byte("stock not enough")) || 
		   bytes.Contains([]byte(errorMsg), []byte("库存不足")) {
			ct.result.StockErrors++
		} else {
			ct.result.OtherErrors++
		}
	}
	
	// 更新时间统计
	if duration > ct.result.MaxTime {
		ct.result.MaxTime = duration
	}
	if duration < ct.result.MinTime {
		ct.result.MinTime = duration
	}
	
	// 计算平均时间（简化计算）
	ct.result.AverageTime = (ct.result.AverageTime*time.Duration(ct.result.TotalRequests-1) + duration) / time.Duration(ct.result.TotalRequests)
}

// 场景1：超卖防护测试
func (ct *ConcurrentTester) TestOversellProtection(productID uint, expectedStock int, concurrentUsers int) {
	fmt.Printf("\n=== 场景1：超卖防护测试 ===\n")
	fmt.Printf("商品ID: %d, 预期库存: %d, 并发用户数: %d\n", productID, expectedStock, concurrentUsers)
	
	var wg sync.WaitGroup
	
	// 启动多个goroutine模拟并发用户
	for i := 1; i <= concurrentUsers; i++ {
		wg.Add(1)
		go func(userID int) {
			defer wg.Done()
			
			success, duration, errorMsg := ct.sendOrderRequest(uint(userID), productID, 1)
			ct.updateResult(success, duration, errorMsg)
			
			if success {
				fmt.Printf("✅ 用户%d: 订单成功 (耗时: %v)\n", userID, duration)
			} else {
				fmt.Printf("❌ 用户%d: %s (耗时: %v)\n", userID, errorMsg, duration)
			}
		}(i)
	}
	
	wg.Wait()
	ct.printResults()
	
	// 验证结果
	if ct.result.SuccessCount <= expectedStock {
		fmt.Printf("✅ 超卖防护测试通过: 成功订单数(%d) <= 预期库存(%d)\n", ct.result.SuccessCount, expectedStock)
	} else {
		fmt.Printf("❌ 超卖防护测试失败: 成功订单数(%d) > 预期库存(%d)\n", ct.result.SuccessCount, expectedStock)
	}
}

// 场景2：不同商品并发测试
func (ct *ConcurrentTester) TestDifferentProductsConcurrency(productIDs []uint, usersPerProduct int) {
	fmt.Printf("\n=== 场景2：不同商品并发测试 ===\n")
	fmt.Printf("商品数量: %d, 每个商品的用户数: %d\n", len(productIDs), usersPerProduct)
	
	var wg sync.WaitGroup
	
	for _, productID := range productIDs {
		for i := 1; i <= usersPerProduct; i++ {
			wg.Add(1)
			go func(pid uint, userID int) {
				defer wg.Done()
				
				success, duration, errorMsg := ct.sendOrderRequest(uint(userID), pid, 1)
				ct.updateResult(success, duration, errorMsg)
				
				if success {
					fmt.Printf("✅ 商品%d-用户%d: 订单成功 (耗时: %v)\n", pid, userID, duration)
				} else {
					fmt.Printf("❌ 商品%d-用户%d: %s (耗时: %v)\n", pid, userID, errorMsg, duration)
				}
			}(productID, i)
		}
	}
	
	wg.Wait()
	ct.printResults()
}

// 打印测试结果
func (ct *ConcurrentTester) printResults() {
	fmt.Printf("\n=== 测试结果统计 ===\n")
	fmt.Printf("总请求数: %d\n", ct.result.TotalRequests)
	fmt.Printf("成功订单: %d\n", ct.result.SuccessCount)
	fmt.Printf("失败订单: %d\n", ct.result.FailureCount)
	fmt.Printf("库存不足: %d\n", ct.result.StockErrors)
	fmt.Printf("其他错误: %d\n", ct.result.OtherErrors)
	fmt.Printf("平均响应时间: %v\n", ct.result.AverageTime)
	fmt.Printf("最大响应时间: %v\n", ct.result.MaxTime)
	fmt.Printf("最小响应时间: %v\n", ct.result.MinTime)
	fmt.Printf("成功率: %.2f%%\n", float64(ct.result.SuccessCount)/float64(ct.result.TotalRequests)*100)
}

// 重置测试结果
func (ct *ConcurrentTester) Reset() {
	ct.mutex.Lock()
	defer ct.mutex.Unlock()
	ct.result = TestResult{
		MinTime: time.Hour,
	}
}

// 主测试函数
func RunConcurrentTests() {
	fmt.Println("🚀 开始并发测试...")
	
	// 创建测试器
	tester := NewConcurrentTester("http://localhost:8080")
	
	// 等待服务器启动
	fmt.Println("等待服务器启动...")
	time.Sleep(2 * time.Second)
	
	// 场景1：测试商品3的超卖防护（假设库存为5）
	tester.TestOversellProtection(3, 5, 10)
	
	// 重置统计
	tester.Reset()
	
	// 场景2：测试不同商品的并发处理
	tester.TestDifferentProductsConcurrency([]uint{1, 2, 3, 4}, 3)
	
	fmt.Println("\n🎉 并发测试完成！")
}
