package main

import "fmt"

func main() {
	//make函数
	studentAges := make(map[string]int)

	//字面量初始化
	scores := map[string]int{
		"张三": 90,
		"李四": 80,
	}
	fmt.Println(studentAges)
	fmt.Println(scores)

	//修改key的值，如果key不存在，就是新增
	scores["张三"] = 100
	fmt.Println(scores)

	//直接访问
	lisiScore := scores["李四"]
	fmt.Println("李四的分数是:", lisiScore)

	//comma ok的用法，用于判断key是否存在
	wangwuScore, ok := scores["王五"]
	if ok {
		fmt.Println("王五的分数是:", wangwuScore)
	} else {
		fmt.Println("查无此人：王五")
	}

	//删除key
	delete(scores, "张三")
	fmt.Println(scores)

	//for range循环取值
	scores = map[string]int{"张三": 95, "李四": 88, "王五": 76}
	for name, score := range scores {
		fmt.Printf("姓名: %s, 分数: %d\n", name, score)
	}
}
