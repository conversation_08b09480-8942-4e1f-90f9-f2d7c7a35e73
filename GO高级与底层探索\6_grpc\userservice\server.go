package main

import (
	"context"
	"log"
	"net"
	pb "userservice/proto"

	"google.golang.org/grpc" // 导入gRPC包
)

// 定义server结构体
// server 结构体需要实现 UserServiceServer 接口
// 必须嵌入UnimplementEduserServiceserver，以具有远程兼容的实现。
type server struct {
	pb.UnimplementedUserServiceServer
}

// 实现GetUser方法，进而实现UserServiceServer接口
func (s *server) GetUser(ctx context.Context, req *pb.UserRequest) (*pb.UserResponse, error) {
	// 在这里实现返回硬编码用户的逻辑
	log.Printf("收到了GetUser请求，ID: %d", req.GetId())

	// 返回硬编码的用户信息
	return &pb.UserResponse{
		Id:    req.GetId(),
		Name:  "张三",
		Email: "<EMAIL>",
	}, nil
}

func main() {
	//监听本地网络地址
	lis, err := net.Listen("tcp", ":50051")
	if err != nil {
		log.Fatalf("监听失败: %v", err)
	}
	//创建一个没有注册服务且尚未开始接受请求的 gRPC 服务器。
	s := grpc.NewServer()
	//注册UserServiceServer服务
	pb.RegisterUserServiceServer(s, &server{})

	log.Println("gRPC服务器正在监听 :50051")
	if err := s.Serve(lis); err != nil {
		log.Fatalf("启动gRPC服务器失败: %v", err)
	}
}
