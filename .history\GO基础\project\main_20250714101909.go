package main

import "fmt"

//数据结构
type Task struct {
	ID        int    //任务的唯一编号
	Content   string //任务的具体内容
	Completed bool   //任务是否已完成
}

//新增任务
func AddTask(Content string) {
	tasks = append(tasks, Task{
		ID:        len(tasks) + 1,
		Content:   Content,
		Completed: false,
	},
	)
}

//查看所有任务
func ListTask() {
	for _, task := range tasks {
		status := "[❌]"
		if task.Completed {
			status = "[✅]"
		}
		fmt.Println(status, task.ID, ".", task.Content)
	}
}

//标记任务完成
func MarkAsDone(ID int) {
	for i, task := range tasks {
		if task.ID == ID {
			// fmt.Printf("TaskID: %d, index: %d\n", task.ID, i)
			tasks[i].Completed = true
		}
	}
}

//删除任务
func DeleteTask(ID int) {
	for i, task := range tasks {
		if task.ID == ID {
			fmt.Printf("TaskID: %d, index: %d\n", task.ID, i)
			//内存效率：这种方法会创建新的底层数组，原有元素会被复制
			tasks = append(tasks[:i], tasks[i+1:]...) //... 操作符：将 slice 展开为单个元素传递给 append
		}
	}
}

//容器存放任务列表
var tasks []Task

func main() {
	fmt.Println(len(tasks))
	AddTask("学习Go语言")
	AddTask("学习Python")
	AddTask("学习Java")
	MarkAsDone(1)
	MarkAsDone(2)
	MarkAsDone(3)
	ListTask()
	DeleteTask(1)
	ListTask()
}
