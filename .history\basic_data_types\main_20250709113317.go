package main

import "fmt"

func main() {
	//声明并初始化各种基础类型的变量
	var name string = "笔记本电脑"
	var quantity int = 100
	var price float64 = 7999.99
	var onSale bool = true

	//打印变量的值
	fmt.Println("商品名称:", name)
	fmt.Println("库存数量:", quantity)
	fmt.Println("商品价格:", price)
	fmt.Println("是否在售:", onSale)

	//使用printf格式化输出为一行
	fmt.Printf("商品名称: %s,库存数量: %d,商品价格: %.2f 元,是否在售: %t\n", name, quantity, price, onSale)
}
