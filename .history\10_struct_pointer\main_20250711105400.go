package main

import "fmt"

//定义学生的结构体类型
type Student struct {
	name   string
	age    int
	score  int
	isMale bool
}

//图书管理
type Book struct {
	Title  string
	Author string
	Pages  int
	IsRead bool
}

//标记为已读
func markAsRead(b *Book) {
	b.IsRead = true
}

func main() {
	// //创建一个Students类型的变量（实例化）
	// var s1 Student
	// s1.name = "张三"
	// s1.age = 18
	// s1.score = 90
	// s1.isMale = true

	// //使用字面量更简洁的创建实例
	// s2 := Student{
	// 	name:   "李四",
	// 	age:    19,
	// 	score:  80,
	// 	isMale: true,
	// }

	// fmt.Printf("学生s1的信息: 姓名-%s, 年龄-%d, 成绩-%d, 性别-%t\n", s1.name, s1.age, s1.score, s1.isMale)
	// fmt.Printf("学生s2的信息: 姓名-%s, 年龄-%d, 成绩-%d, 性别-%t\n", s2.name, s2.age, s2.score, s2.isMale)

	b1 := Book{
		Title:  "Go语言程序设计",
		Author: "<PERSON><PERSON>",
		Pages:  500,
		IsRead: false,
	}

	fmt.Printf("图书b1的信息: 书名-%s, 作者-%s, 页数-%d, 是否已读-%t\n", b1.Title, b1.Author, b1.Pages, b1.IsRead)
	markAsRead(&b1, true)
	fmt.Printf("图书b1的信息: 书名-%s, 作者-%s, 页数-%d, 是否已读-%t\n", b1.Title, b1.Author, b1.Pages, b1.IsRead)
}
