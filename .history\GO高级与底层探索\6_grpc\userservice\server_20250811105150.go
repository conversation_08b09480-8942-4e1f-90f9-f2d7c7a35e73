package userservice

import (
	pb "grpc/userservice/proto"
)

// 定义server结构体
// server 结构体需要实现 UserServiceServer 接口
// 必须嵌入UnimplementEduserServiceserver，以具有远程兼容的实现。
type server struct {
	pb.UnimplementedUserServiceServer
}

//实现GetUser方法，进而实现UserServiceServer接口
func (s *server) GetUser(ctx context.Context, req *pb.UserRequest) (*pb.UserResponse, error) {
	// 在这里实现返回硬编码用户的逻辑
	log.Printf("收到了GetUser请求，ID: %d", req.GetId())
	// ...
	return &pb.UserResponse{...}, nil
}
