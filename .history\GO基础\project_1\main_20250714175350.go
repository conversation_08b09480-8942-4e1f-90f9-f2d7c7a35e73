package main

import (
	"bufio"
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"strings"
)

// 数据结构
type Task struct {
	ID        int    //任务的唯一编号
	Content   string //任务的具体内容
	Completed bool   //任务是否已完成
}

// 全局ID，唯一
var NextID int = 1

// 新增任务
func AddTask(t []Task, Content string) []Task {
	t = append(t, Task{
		ID:        NextID,
		Content:   Content,
		Completed: false,
	},
	)
	NextID++
	// 自动保存到文件
	saveTasksToFile(t)
	return t
}

// 查看所有任务
func ListTask(t []Task) {
	for _, task := range t {
		status := "[❌]"
		if task.Completed {
			status = "[✅]"
		}
		fmt.Println(status, task.ID, ".", task.Content)
	}
}

// 标记任务完成
func MarkAsDone(t []Task, ID int) bool {
	found := false
	for i, task := range t {
		if task.ID == ID {
			// fmt.Printf("TaskID: %d, index: %d\n", task.ID, i)
			t[i].Completed = true
			found = true
			break //找到后退出循环,避免性能消耗
		}
	}
	// 自动保存到文件
	saveTasksToFile(t)
	return found
}

// 删除任务
func DeleteTask(t []Task, ID int) ([]Task, bool) {
	found := false
	for i, task := range t {
		if task.ID == ID {
			// fmt.Printf("TaskID: %d, index: %d\n", task.ID, i)
			//内存效率：这种方法会创建新的底层数组，原有元素会被复制
			t = append(t[:i], t[i+1:]...) //... 操作符：将 slice 展开为单个元素传递给 append
			found = true
			break //找到后退出循环,避免性能消耗
		}
	}
	// 自动保存到文件
	saveTasksToFile(t)
	return t, found
}

// 保存任务到JSON文件
func saveTasksToFile(t []Task) {
	data, err := json.MarshalIndent(t, "", "  ")
	if err != nil {
		fmt.Printf("\033[1;31m保存任务失败: %v\033[0m\n", err)
		return
	}

	err = os.WriteFile("tasks.json", data, 0644)
	if err != nil {
		fmt.Printf("\033[1;31m写入文件失败: %v\033[0m\n", err)
	}
}

// 从JSON文件加载任务
func loadTasksFromFile() []Task {
	var tasks []Task
	data, err := os.ReadFile("tasks.json")
	if err != nil {
		// 文件不存在是正常的，第一次运行时
		if os.IsNotExist(err) {
			fmt.Println("\033[1;33m首次运行，创建新的任务列表\033[0m")
			return tasks
		}
		fmt.Printf("\033[1;31m读取文件失败: %v\033[0m\n", err)
		return tasks
	}

	err = json.Unmarshal(data, &tasks)
	if err != nil {
		fmt.Printf("\033[1;31m解析JSON失败: %v\033[0m\n", err)
	} else {
		fmt.Printf("\033[1;32m成功加载 %d 个任务\033[0m\n", len(t))
	}
	return tasks
}

func main() {
	// 容器存放任务列表
	var tasks []Task
	// 程序启动时加载任务数据
	loadTasksFromFile(tasks)
	for _, v := range tasks {
		if v.ID >= NextID {
			NextID = v.ID + 1
		}
	}
	for {
		var command string
		fmt.Print("请输入以下命令执行操作>\n1.add 添加任务\n2.list 查看任务列表\n3.done 标记任务完成\n4.delete 删除任务\n5.exit 退出程序\n> ")
		fmt.Scanln(&command) //读取用户输入的一整行，存入command变量
		switch command {
		case "add":
			//对于读取任务内容等可能包含空格的输入，使用 bufio 会更健壮
			reader := bufio.NewReader(os.Stdin)
			fmt.Print("请输入任务内容> ")
			input, _ := reader.ReadString('\n')
			content := strings.TrimSpace(input) //去掉首尾的空白和换行符
			tasks = AddTask(tasks, content)
			fmt.Println("\033[1;32m任务添加成功\033[0m")
		case "list":
			fmt.Println("\033[1;32m任务列表如下\033[0m")
			ListTask(tasks)
		case "done":
			var input string
			fmt.Scanln(&input)
			fmt.Print("请输入要标记为完成的任务ID> ")
			doneID, err := strconv.Atoi(input)
			if err != nil {
				fmt.Println("\033[1;31m无效的ID,请输入一个数字! \033[0m")
				continue
			}
			fmt.Scanln(&doneID) //读取用户输入的一整行，存入doneID变量，并转换为int类型，如果输入的不是数字，则会报错
			if MarkAsDone(tasks, doneID) {
				fmt.Printf("\033[1;32m任务%d标记为完成\033[0m\n", doneID)
			} else {
				fmt.Printf("\033[1;31m任务%d未找到\033[0m\n", doneID)
			}
		case "delete":
			var deleteID int
			fmt.Print("请输入要删除的任务ID> ")
			fmt.Scanln(&deleteID) //读取用户输入的一整行，存入deleteID变量，并转换为int类型，如果输入的不是数字，则会报错
			if DeleteTask(tasks, deleteID) {
				fmt.Printf("\033[1;32m任务%d删除成功\033[0m\n", deleteID)
			} else {
				fmt.Printf("\033[1;31m任务%d未找到\033[0m\n", deleteID)
			}
		case "exit":
			fmt.Println("退出程序")
			return
		default:
			fmt.Println("\033[1;31m无效的命令,请重新输入\033[0m")
		}
	}
}
