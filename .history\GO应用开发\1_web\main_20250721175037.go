package main

import (
	"fmt"
	"log"
	"net/http"
)

// 定义处理器函数
// 处理"/hello"请求
// 定义厨师
// w是服务员，负责把响应数据返回给客户端（把做好的菜端给客人）
func helloHandler(w http.ResponseWriter, r *http.Request) {
	// Fprintf 类似于 Printf，但它不是打印到控制台，而是将内容写入到一个“Writer”中，这里的 w 就是一个Writer。
	fmt.Fprintf(w, "Hello, World! Welcome to your first Go web server!")
}

func main() {
	// 注册处理器
	// 给厨师分配菜单
	http.HandleFunc("/hello", helloHandler)

	fmt.Println("服务器即将启动，请访问 http://localhost:8080/hello")

	// 启动服务器
	// 开门营业，监听8080端口，看是否有人来吃饭
	// ":8080" 表示在本地的8080端口上进行监听。
	// nil 表示使用Go默认的请求调度器（我们刚才用HandleFunc注册的那个）。
	// 这个函数会一直运行（阻塞），直到程序被手动停止。
	err := http.ListenAndServe(":8080", nil)
	if err != nil {
		log.Fatal("服务器启动失败: ", err)
	}
}
