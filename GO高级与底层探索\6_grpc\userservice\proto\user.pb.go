// 指定使用proto3语法

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.7
// 	protoc        v6.31.1
// source: userservice/proto/user.proto

// 定义包名，这会映射到Go的包名

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 定义请求消息
type UserRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 字段类型 字段名 = 字段编号;
	// 编号是唯一的，用于在二进制格式中识别字段
	Id            int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserRequest) Reset() {
	*x = UserRequest{}
	mi := &file_userservice_proto_user_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserRequest) ProtoMessage() {}

func (x *UserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_userservice_proto_user_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserRequest.ProtoReflect.Descriptor instead.
func (*UserRequest) Descriptor() ([]byte, []int) {
	return file_userservice_proto_user_proto_rawDescGZIP(), []int{0}
}

func (x *UserRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 定义响应消息
type UserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Email         string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserResponse) Reset() {
	*x = UserResponse{}
	mi := &file_userservice_proto_user_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserResponse) ProtoMessage() {}

func (x *UserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_userservice_proto_user_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserResponse.ProtoReflect.Descriptor instead.
func (*UserResponse) Descriptor() ([]byte, []int) {
	return file_userservice_proto_user_proto_rawDescGZIP(), []int{1}
}

func (x *UserResponse) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserResponse) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

var File_userservice_proto_user_proto protoreflect.FileDescriptor

const file_userservice_proto_user_proto_rawDesc = "" +
	"\n" +
	"\x1cuserservice/proto/user.proto\x12\x05proto\"\x1d\n" +
	"\vUserRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\"H\n" +
	"\fUserResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x14\n" +
	"\x05email\x18\x03 \x01(\tR\x05email2A\n" +
	"\vUserService\x122\n" +
	"\aGetUser\x12\x12.proto.UserRequest\x1a\x13.proto.UserResponseB\x13Z\x11userservice/protob\x06proto3"

var (
	file_userservice_proto_user_proto_rawDescOnce sync.Once
	file_userservice_proto_user_proto_rawDescData []byte
)

func file_userservice_proto_user_proto_rawDescGZIP() []byte {
	file_userservice_proto_user_proto_rawDescOnce.Do(func() {
		file_userservice_proto_user_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_userservice_proto_user_proto_rawDesc), len(file_userservice_proto_user_proto_rawDesc)))
	})
	return file_userservice_proto_user_proto_rawDescData
}

var file_userservice_proto_user_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_userservice_proto_user_proto_goTypes = []any{
	(*UserRequest)(nil),  // 0: proto.UserRequest
	(*UserResponse)(nil), // 1: proto.UserResponse
}
var file_userservice_proto_user_proto_depIdxs = []int32{
	0, // 0: proto.UserService.GetUser:input_type -> proto.UserRequest
	1, // 1: proto.UserService.GetUser:output_type -> proto.UserResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_userservice_proto_user_proto_init() }
func file_userservice_proto_user_proto_init() {
	if File_userservice_proto_user_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_userservice_proto_user_proto_rawDesc), len(file_userservice_proto_user_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_userservice_proto_user_proto_goTypes,
		DependencyIndexes: file_userservice_proto_user_proto_depIdxs,
		MessageInfos:      file_userservice_proto_user_proto_msgTypes,
	}.Build()
	File_userservice_proto_user_proto = out.File
	file_userservice_proto_user_proto_goTypes = nil
	file_userservice_proto_user_proto_depIdxs = nil
}
