package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
)

// User结构体
type User struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
	Role string `json:"role"`
}

// Book结构体
type Book struct {
	Title  string `json:"title"`
	Author string `json:"author"`
}

// 处理器A: 处理根路径 "/"
func rootHandler(w http.ResponseWriter, r *http.Request) {
	fmt.Fprintf(w, "欢迎来到首页！")
}

// 处理器B: 处理 "/about" 路径
func aboutHandler(w http.ResponseWriter, r *http.Request) {
	fmt.Fprintf(w, "这是关于我们的页面。")
}

// 处理器C: 处理 "/greet" 路径
// 例：http://localhost:8080/greet?name=Alice，?后面的就是url参数
func greetHandle(w http.ResponseWriter, r *http.Request) {
	//r.URL.Query() 会解析URL中的所有查询参数
	// .Get("name") 会获取键为 "name" 的那个参数的值
	name := r.URL.Query().Get("name")
	if name == "" {
		name = "Gopher"
	}
	fmt.Fprintf(w, "你好, %s!", name)
}

// 处理器D: 处理 "/api/user" 路径
func userHandler(w http.ResponseWriter, r *http.Request) {
	//1.创建一个user实例
	user := User{
		ID:   101,
		Name: "Alice",
		Role: "Admin",
	}

	//2.将user实例编码为JSON
	jsonData, err := json.Marshal(user)
	if err != nil {
		//如果编码失败，返回一个服务器内部错误
		http.Error(w, "无法编码JSON", http.StatusInternalServerError)
		return
	}

	//3.设置Content-Type头
	w.Header().Set("Content-Type", "application/json")

	//4.写入JSON数据到响应
	w.Write(jsonData)
}

func bookHandler(w http.ResponseWriter, r *http.Request, bookDB map[string]Book) {
	//1. 获取id
	ID := r.URL.Query().Get("id")
	if ID == "" {
		http.Error(w, "无法获取id", http.StatusBadRequest)
		return
	}

	//2. 根据id查询书籍
	book, ok := bookDB[ID]
	if !ok {
		http.Error(w, "无法找到书籍", http.StatusNotFound)
		return
	}

	//3. 将书籍编码为JSON
	jsonData, err := json.Marshal(book)
	if err != nil {
		http.Error(w, "无法编码JSON", http.StatusInternalServerError)
		return
	}

	//4. 设置Content-Type头
	w.Header().Set("Content-Type", "application/json")

	//5. 写入JSON数据到响应
	w.Write(jsonData)

}

func main() {
	http.HandleFunc("/", rootHandler)
	http.HandleFunc("/about", aboutHandler)
	http.HandleFunc("/greet", greetHandle)
	http.HandleFunc("/api/user", userHandler)

	log.Println("服务器即将启动，请访问 http://localhost:8080/")

	err := http.ListenAndServe(":8080", nil)
	if err != nil {
		fmt.Println("服务器启动失败: ", err)
	}

	var bookDB = map[string]Book{
		"101": {Title: "Go语言圣经", Author: "Alan Donovan"},
		"102": {Title: "代码整洁之道", Author: "Robert C. Martin"},
	}
}
