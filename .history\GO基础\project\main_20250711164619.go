package main

import "fmt"

//数据结构
type Task struct {
	ID        int    //任务的唯一编号
	Content   string //任务的具体内容
	Completed bool   //任务是否已完成
}

//新增任务
func addTask(Content string) {
	tasks = append(tasks, Task{
		ID:        len(tasks) + 1,
		Content:   Content,
		Completed: false,
	},
	)
}

//查看所有任务
func ListTask() {
	for _, task := range tasks {
		status := "[❌]"
		if task.Completed {
			status = "[✅]"
		}
		fmt.Println("status, task.ID, task.Content")
	}
}

//标记任务完成

//删除任务

//容器存放任务列表
var tasks []Task

func main() {
	fmt.Println(len(tasks))
}
