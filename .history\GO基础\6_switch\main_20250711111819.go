package main

import "fmt"

func main() {
	day := "星期一"
	//值匹配
	switch day {
	case "星期一":
		fmt.Println("学习go")
	case "星期二":
		fmt.Println("学习python")
	case "星期三":
		fmt.Println("开会")
	case "星期四":
		fmt.Println("星期四")
	case "星期五":
		fmt.Println("星期五")
	case "星期六":
		fmt.Println("星期六")
	case "星期日":
		fmt.Println("星期日")
	default:
		fmt.Println("学习go")
	}

	//条件匹配
	score := 85
	switch {
	case score >= 90:
		fmt.Println("优秀")
	case score >= 80:
		fmt.Println("良好")
	case score >= 70:
		fmt.Println("中等")
	case score >= 60:
		fmt.Println("及格")
	default:
		fmt.Println("不及格")
	}

	for i := 1; i <= 100; i++ {
		switch {
		//把需要满足多个条件的放在前列
		case i%3 == 0 && i%5 == 0:
			fmt.Println(i, "FizzBuzz")
		case i%3 == 0:
			fmt.Println(i, "Fizz")
		case i%5 == 0:
			fmt.Println(i, "Buzz")
		default:
			fmt.Println(i)
		}
	}
}
