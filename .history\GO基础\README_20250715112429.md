# Go基础注意事项

## 1. helloworld
- [为 Go 程序添加 Windows 图标和版本信息](./1_helloworld/README.md)

## 2. 变量-常量
变量值可以改变，常量值不能改变，全局变量和全局常量可以在多个函数中使用。
变量的几种声明方式：
- 标准声明
- 声明并初始化
- 类型推导
- 短变量声明（最常用）
- 全局变量
- 全局常量

## 3. 基础数据类型
Go 语言有以下几种基础数据类型：
- 布尔型
- 数字类型
- 字符串类型
- 浮点数类型

## 4. if-else
Go 语言的 if-else 语句用于根据条件执行不同的代码块。
if 语句的几种形式：
- 标准 if 语句
- if-else 语句
- if-else if-else 语句

## 5. for
Go 语言的 for 语句用于执行循环操作。
for 语句的几种形式：
- 标准 for 语句
- for-range 语句
- 无限循环
- break 和 continue 语句
- goto 语句

## 6. switch
Go 语言的 switch 语句用于根据条件执行不同的代码块。
switch 语句的几种形式：
- 值匹配
- 条件匹配
- 多重匹配
- fallthrough 语句
- switch 语句的表达式可以是任何类型
- switch 语句的 case 语句可以是任何类型
- switch 语句的 case 语句可以有多个值
- switch 语句的 case 语句可以有多个条件
- switch 语句的 case 语句可以有多个语句

## 7. 数组
Go 语言的数组用于存储多个相同类型的数据。
数组的几种形式：
- 声明数组
- 初始化数组
- 访问数组元素
- 修改数组元素
- 数组的长度
- 数组的遍历

## 8. 切片
Go 语言的切片用于存储多个相同类型的数据。
切片的几种形式：
- 声明切片
- 初始化切片
- 访问切片元素
- 修改切片元素
- 切片的长度
- 切片的容量
- 切片的遍历
- 切片的 append
- 切片的 copy
- 切片的 make
  
### **Go切片（Slice）核心注意事项清单**

#### **一、 关于函数传参和返回值**

1.  **黄金法则：修改即返回**
    只要你的函数中使用了 `append` 或者对切片本身进行再切片赋值（如 `s = s[1:]`），导致切片的**长度、容量或指向的底层数组可能发生改变**，那么这个函数就**必须返回**修改后的新切片。调用方也**必须接收**这个返回值。

    ```go
    // 正确示范
    func deleteElement(s []int, index int) []int {
        s = append(s[:index], s[index+1:]...)
        return s
    }
    mySlice = deleteElement(mySlice, 1)
    ```

2.  **仅修改元素内容时，可不返回**
    如果函数只是修改切片中元素的值（例如 `s[i].Field = ...`），而没有改变切片的长度或容量，那么可以不返回切片，因为函数内外指向的是同一个底层数组。

#### **二、 关于创建和初始化**

3.  **优先使用 `make` 创建切片**
    当你能预估到切片大概需要存储多少元素时，使用 `make([]T, length, capacity)` 来创建切片。预先分配容量可以极大地减少因`append`导致的内存重分配和数据拷贝次数，显著提升性能。

    ```go
    // 预估需要存100个元素，直接分配100的容量
    mySlice := make([]int, 0, 100) 
    ```

4.  **警惕 `nil` 切片和空切片的区别**

      * `var s []int`：这是一个 `nil` 切片，它没有指向任何底层数组。
      * `s := make([]int, 0)` 或 `s := []int{}`：这是一个空切片，它指向一个长度为0的底层数组。
      * 对`nil`切片和空切片执行`append`、`len`和`cap`都是安全的，行为也一致。但在JSON编码等场景下，`nil`切片可能会被编码为`null`，而空切片会被编码为`[]`，需要注意区分。

#### **三、 关于底层数组和内存共享**

5.  **切片是“共享”的，修改需谨慎**
    对一个切片进行再切片（`sliceB := sliceA[1:5]`），`sliceA` 和 `sliceB` 共享同一个底层数组。修改 `sliceB` 中元素的值，会直接影响到 `sliceA` 中对应的元素。

6.  **`append` 可能会“背叛”你**
    如果对共享底层数组的切片`sliceB`进行`append`操作，一旦**超过了底层数组的容量**，`sliceB`会指向一个全新的、更大的底层数组。此时，`sliceB`就和`sliceA`“分道扬镳”了，后续对`sliceB`的修改将不再影响`sliceA`。这个特性非常微妙，是很多隐藏bug的来源。

#### **四、 关于遍历**

7.  **`for...range` 遍历时，`value` 是一个副本**
    在 `for i, value := range mySlice` 循环中，每次循环得到的 `value` 是切片中元素的一个**拷贝**。如果你想修改切片中的元素，必须通过索引来操作：`mySlice[i]`。
    ```go
    // 错误示范：无法修改原始切片
    for _, task := range tasks {
        task.Completed = true 
    }
    // 正确示范
    for i := range tasks {
        tasks[i].Completed = true
    }
    ```

#### **五、 关于切片操作**

8.  **切片操作 `s[low:high]` 是半开区间**
    它包含索引 `low` 的元素，但不包含索引 `high` 的元素。长度为 `high - low`。

9.  **理解容量（Capacity）的计算**
    一个切片 `s := arr[low:high]` 的容量，是从 `low` 索引开始，一直到底层数组 `arr` 的末尾。`cap(s) = cap(arr) - low`。

## 9. map
Go 语言的 map 用于存储键值对的数据。
map 的几种形式：
- 声明 map
- 初始化 map
- 访问 map 元素
- 修改 map 元素
- 删除 map 元素
- map 的长度
- map 的遍历
- map 的 make
- map 的字面量初始化
- map 的 comma ok 用法
- map 的 for range 循环

## 10. 结构体-指针
Go 语言的结构体用于存储多个不同类型的数据。
结构体的几种形式：
- 声明结构体
- 初始化结构体
- 访问结构体元素
- 修改结构体元素
- 结构体的指针
- 结构体的匿名字段
- 结构体的嵌套
- 结构体的方法
- 结构体的字符串方法
- 结构体的标签
