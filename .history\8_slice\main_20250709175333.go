package main

import "fmt"

func main() {
	//直接声明
	s1 := []string{"苹果", "梨", "香蕉"}
	fmt.Printf("s1: %v, s1的长度: %d, s1的容量: %d\n", s1, len(s1), cap(s1))

	//使用make函数创建
	s2 := make([]string, 3, 5)
	fmt.Printf("s2: %v, s2的长度: %d, s2的容量: %d\n", s2, len(s2), cap(s2))
	s2[0] = "GO"
	s2[1] = "Python"
	s2[2] = "Java"
	// s2[3] = "C++" //索引超出范围了 报错
	fmt.Printf("s2: %v, s2的长度: %d, s2的容量: %d\n", s2, len(s2), cap(s2))

	//开始切片
	arr := [5]int{1, 2, 3, 4, 5}
	s3 := arr[1:4]                                                     //左闭右开,索引从1开始，到4结束，不包括4
	fmt.Printf("s3: %v, s3的长度: %d, s3的容量: %d\n", s3, len(s3), cap(s3)) //容量是指针索引到底层数组最后一个元素的索引，也就是[1:5]

	//切片append
	var fruits []string
	fruits = append(fruits, "西瓜")
	fruits = append(fruits, "哈密瓜", "草莓")
	fmt.Println("水果切片:", fruits)
	fmt.Printf("fruits: %v, fruits的长度: %d, fruits的容量: %d\n", fruits, len(fruits), cap(fruits))

	shoppingList := []string{"鸡蛋", "牛奶"}
	fmt.Printf("shoppingList: %v, shoppingList的长度: %d, shoppingList的容量: %d\n", shoppingList, len(shoppingList), cap(shoppingList))
	shoppingList = append(shoppingList, "面包")
	fmt.Printf("shoppingList: %v, shoppingList的长度: %d, shoppingList的容量: %d\n", shoppingList, len(shoppingList), cap(shoppingList))

	for i := 0; i < len(shoppingList); i++ {
		fmt.Printf("第 %d 件商品是: %s\n", i, shoppingList[i])
	}
}
