package main

import (
	"encoding/json"
	"fmt"
	"os"
)

var Dictionary = make(map[string]string)

// 添加单词
func AddWord(key, value string) {
	Dictionary[key] = value
	saveWordsToFile()
}

// 查询单词
func SearchWord(key string) string {
	if value, ok := Dictionary[key]; ok {
		fmt.Printf("\033[1;32m单词释义: %s\033[0m\n", value)
	} else {
		return "查无此词"
	}
	return ""
}

// 删除单词
func DeleteWord(key string) {
	delete(Dictionary, key)
	saveWordsToFile()
}

// 单词列表
func ListWords() {
	for key, value := range Dictionary {
		fmt.Println(key, ":", value)
	}
}

// 保存单词到文件
func saveWordsToFile() {
	// TODO: 保存单词到JSON文件
	data, err := json.Marshal(Dictionary)
	if err != nil {
		fmt.Printf("\033[1;31m保存单词失败: %v\033[0m\n", err)
	}
	err = os.WriteFile("dictionary.json", data, 0644)
	if err != nil {
		fmt.Printf("\033[1;31m写入文件失败: %v\033[0m\n", err)
	}
	fmt.Println("\033[1;32m单词保存成功\033[0m")
}

// 从文件加载单词
func loadWordsFromFile() {
	data, err := os.ReadFile("dictionary.json")
	if err != nil {
		// 文件不存在是正常的，第一次运行时
		if os.IsNotExist(err) {
			fmt.Println("\033[1;33m首次运行，创建新的单词列表\033[0m")
			return
		}
		fmt.Printf("\033[1;31m读取文件失败: %v\033[0m\n", err)
		return
	}

	err = json.Unmarshal(data, &Dictionary)
	if err != nil {
		fmt.Printf("\033[1;31m解析JSON失败: %v\033[0m\n", err)
	} else {
		fmt.Printf("\033[1;32m成功加载 %d 个单词\033[0m\n", len(Dictionary))
	}
}

func main() {
	loadWordsFromFile()
	for {
		var command string
		fmt.Print("请输入以下命令执行操作>\n1.add 添加单词\n2.search 查询单词\n3.delete 删除单词\n4.list 单词列表\n5.exit 退出程序\n> ")
		fmt.Scanln(&command) //读取用户输入的一整行，存入command变量
		switch command {
		case "add":
			var key, value string
			fmt.Print("请输入单词> ")
			fmt.Scanln(&key)
			fmt.Print("请输入释义> ")
			fmt.Scanln(&value)
			AddWord(key, value)
			fmt.Println("\033[1;32m单词添加成功\033[0m")
		case "search":
			var key string
			fmt.Print("请输入要查询的单词> ")
			fmt.Scanln(&key)
			value := SearchWord(key)
			if value == "查无此词" {
				fmt.Println("\033[1;31m查无此词\033[0m")
			}
		case "delete":
			var key string
			fmt.Print("请输入要删除的单词> ")
			fmt.Scanln(&key)
			DeleteWord(key)
			fmt.Println("\033[1;32m单词删除成功\033[0m")
		case "list":
			ListWords()
		case "exit":
			fmt.Println("退出程序")
			return
		default:
			fmt.Println("无效的命令，请重新输入")
		}
	}
}
