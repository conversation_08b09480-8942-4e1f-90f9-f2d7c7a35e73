// 指定使用proto3语法
syntax = "proto3";

// 定义包名，这会映射到Go的包名
package proto;

// 为了让Go编译器知道生成的代码应该放在哪个包下
option go_package = "userservice/proto";

// 定义UserService服务
service UserService {
  // 定义一个GetUser方法
  rpc GetUser (UserRequest) returns (UserResponse);
}

// 定义请求消息
message UserRequest {
  // 字段类型 字段名 = 字段编号;
  // 编号是唯一的，用于在二进制格式中识别字段
  int32 id = 1;
}

// 定义响应消息
message UserResponse {
  int32 id = 1;
  string name = 2;
  string email = 3;
}