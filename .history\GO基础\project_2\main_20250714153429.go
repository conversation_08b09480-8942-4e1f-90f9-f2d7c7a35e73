package main

import "fmt"

var Dictionary = make(map[string]string)

// 添加单词
func AddWord(key, value string) {
	Dictionary[key] = value
}

// 查询单词
func SearchWord(key string) string {
	if value, ok := Dictionary[key]; ok {
		fmt.Printf("\033[1;32m%s\033[0m\n", value)
	}
	return "查无此词"
}

// 删除单词
func DeleteWord(key string) {
	delete(Dictionary, key)
}

// 单词列表
func ListWords() {
	for key, value := range Dictionary {
		fmt.Println(key, ":", value)
	}
}

func main() {
	for {
		var command string
		fmt.Print("请输入以下命令执行操作>\n1.add 添加单词\n2.search 查询单词\n3.delete 删除单词\n4.list 单词列表\n5.exit 退出程序\n> ")
		fmt.Scanln(&command) //读取用户输入的一整行，存入command变量
		switch command {
		case "add":
			var key, value string
			fmt.Print("请输入单词> ")
			fmt.Scanln(&key)
			fmt.Print("请输入释义> ")
			fmt.Scanln(&value)
			AddWord(key, value)
			fmt.Println("\033[1;32m单词添加成功\033[0m")
		case "search":
			var key string
			fmt.Print("请输入要查询的单词> ")
			fmt.Scanln(&key)
			value := SearchWord(key)

	}
}
