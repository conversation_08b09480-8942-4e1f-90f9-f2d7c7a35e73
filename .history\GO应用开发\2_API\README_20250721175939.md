### **第十七课：构建API基础 - 路由、参数与JSON**

#### **1. 处理多个路由**

一个真正的网站或API不可能只有一个URL。我们需要根据不同的路径（Path），返回不同的内容。例如，`/` 返回首页，`/about` 返回关于页面。

这在Go中非常简单：你只需要为你想要的每个路径，都调用一次 `http.HandleFunc` 来注册一个对应的处理器。

```go
// main.go

// 处理器A：处理根路径 "/"
func rootHandler(w http.ResponseWriter, r *http.Request) {
	fmt.Fprintf(w, "欢迎来到首页！")
}

// 处理器B：处理 "/about" 路径
func aboutHandler(w http.ResponseWriter, r *http.Request) {
	fmt.Fprintf(w, "这是关于我们的页面。")
}

func main() {
    http.HandleFunc("/", rootHandler)
    http.HandleFunc("/about", aboutHandler)

    log.Println("服务器已启动，监听端口 8080...")
    // ... 启动服务器 ...
}
```

现在，你访问 `http://localhost:8080/` 和 `http://localhost:8080/about` 将会看到不同的内容。

#### **2. 从URL中读取参数**

静态页面不够有趣，我们需要与用户交互。最简单的交互方式之一，就是通过URL的查询参数（Query Parameters）。

例如这个URL：`http://localhost:8080/greet?name=Alice`

  * `?` 之后的部分 `name=Alice` 就是查询参数。
  * `name` 是参数的键（key）。
  * `Alice` 是参数的值（value）。

我们可以通过处理器函数的第二个参数 `r *http.Request` 来获取这些参数。

```go
// greetHandler 会根据URL中的name参数，向用户问好
func greetHandler(w http.ResponseWriter, r *http.Request) {
	// r.URL.Query() 会解析URL中的所有查询参数
	// .Get("name") 会获取键为 "name" 的那个参数的值
	name := r.URL.Query().Get("name")

	// 如果用户没有提供name参数，我们就给一个默认值
	if name == "" {
		name = "Gopher"
	}

	fmt.Fprintf(w, "你好, %s!", name)
}

func main() {
    // ...
    http.HandleFunc("/greet", greetHandler)
    // ...
}
```

**测试一下**：

1.  访问 `http://localhost:8080/greet` -\> 你会看到 “你好, Gopher\!”
2.  访问 `http://localhost:8080/greet?name=Alice` -\> 你会看到 “你好, Alice\!”

#### **3. 返回JSON数据 (API核心)**

在现代Web开发中，服务器更多时候返回的不是给人看的HTML文本，而是给程序（比如手机APP或前端JavaScript）看的**JSON**数据。

还记得我们用 `encoding/json` 包来读写文件吗？它同样是处理Web API的利器。

**流程如下：**

1.  在Go中创建一个\*\*结构体（Struct）\*\*实例，用来存放你要返回的数据。
2.  使用 `json.Marshal` 将这个结构体实例转换成JSON格式的字节**切片（Slice）**。
3.  在写入响应前，**设置一个HTTP头（Header）**，告诉浏览器：“我给你的是JSON格式的数据”，即 `Content-Type: application/json`。
4.  将JSON字节切片写入到 `http.ResponseWriter`。

<!-- end list -->

```go
import (
	// ...
	"encoding/json" 
)

// 定义一个我们要返回的数据结构
type User struct {
	ID   int    `json:"id"` // json:"..." 标签可以控制JSON输出的字段名
	Name string `json:"name"`
	Role string `json:"role"`
}

// userHandler 返回一个固定的用户信息
func userHandler(w http.ResponseWriter, r *http.Request) {
	// 1. 创建一个User实例
	user := User{
		ID:   101,
		Name: "Alice",
		Role: "Admin",
	}

	// 2. 将User实例编码(Marshal)为JSON
	jsonData, err := json.Marshal(user)
	if err != nil {
		// 如果编码失败，返回一个服务器内部错误
		http.Error(w, "无法编码JSON", http.StatusInternalServerError)
		return
	}

	// 3. 设置Content-Type头
	w.Header().Set("Content-Type", "application/json")

	// 4. 写入JSON数据到响应
	w.Write(jsonData)
}

func main() {
    // ...
    http.HandleFunc("/api/user", userHandler)
    // ...
}
```

**测试一下**：
访问 `http://localhost:8080/api/user`，你的浏览器会显示一段纯文本：
`{"id":101,"name":"Alice","role":"Admin"}`
这就是JSON！你的第一个API接口已经诞生了。

-----

### **实践环节：图书查询API**

现在，请你综合运用今天的知识，为你熟悉的`Book`结构体创建一个简单的查询API。

**任务要求：**

1.  创建一个Web服务器。
2.  在`main`函数中，创建一个`map[string]Book`来模拟一个内存数据库。`key`是书的ID（字符串，如`"101"`），`value`是`Book`结构体。请预先存入2-3本书。
    ```go
    // Book 结构体
    type Book struct {
        Title  string `json:"title"`
        Author string `json:"author"`
    }
    // 模拟数据库
    var bookDB = map[string]Book{
        "101": {Title: "Go语言圣经", Author: "Alan Donovan"},
        "102": {Title: "代码整洁之道", Author: "Robert C. Martin"},
    }
    ```
3.  创建一个处理器，注册到路径 `/api/book`。
4.  这个处理器需要从URL查询参数中获取 `id` (例如: `/api/book?id=101`)。
5.  根据`id`去`bookDB`这个map中查找对应的书。
6.  **如果找到了**，将这本书的结构体编码为JSON并返回给客户端。
7.  **如果没找到**，返回一个表示错误的JSON，例如 `{"error": "Book not found"}`。同时，将HTTP状态码设置为**404 Not Found**。（提示：可以使用 `w.WriteHeader(http.StatusNotFound)` 来设置状态码）。

-----

你现在已经构建出了一个虽然简单但五脏俱全的API服务！然而，当路由变得越来越多时，手动管理会变得很繁琐。在下一课，我们将学习如何使用Go社区最流行的\*\*Web框架（如 Gin）\*\*来简化路由、参数解析等工作，让开发效率更上一层楼。