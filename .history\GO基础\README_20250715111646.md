# Go基础注意事项

## 1. helloworld
- [为 Go 程序添加 Windows 图标和版本信息](./1_helloworld/README.md)

## 2. 变量-常量
变量值可以改变，常量值不能改变，全局变量和全局常量可以在多个函数中使用。
变量的几种声明方式：
- 标准声明
- 声明并初始化
- 类型推导
- 短变量声明（最常用）
- 全局变量
- 全局常量

## 3. 基础数据类型
Go 语言有以下几种基础数据类型：
- 布尔型
- 数字类型
- 字符串类型
- 浮点数类型

## 4. if-else
Go 语言的 if-else 语句用于根据条件执行不同的代码块。
if 语句的几种形式：
- 标准 if 语句
- if-else 语句
- if-else if-else 语句

## 5. for
Go 语言的 for 语句用于执行循环操作。
for 语句的几种形式：
- 标准 for 语句
- for-range 语句
- 无限循环
- break 和 continue 语句
- goto 语句

## 6. switch
Go 语言的 switch 语句用于根据条件执行不同的代码块。
switch 语句的几种形式：
- 值匹配
- 条件匹配
- 多重匹配
- fallthrough 语句
- switch 语句的表达式可以是任何类型
- switch 语句的 case 语句可以是任何类型
- switch 语句的 case 语句可以有多个值
- switch 语句的 case 语句可以有多个条件
- switch 语句的 case 语句可以有多个语句

## 7. 数组
Go 语言的数组用于存储多个相同类型的数据。
数组的几种形式：
- 声明数组
- 初始化数组
- 访问数组元素
- 修改数组元素
- 数组的长度
- 数组的遍历

## 8. 切片
Go 语言的切片用于存储多个相同类型的数据。
切片的几种形式：
- 声明切片
- 初始化切片
- 访问切片元素
- 修改切片元素
- 切片的长度
- 切片的容量
- 切片的遍历
- 切片的 append
- 切片的 copy
- 切片的 make

## 9. map
Go 语言的 map 用于存储键值对的数据。
map 的几种形式：
- 声明 map
- 初始化 map
- 访问 map 元素
- 修改 map 元素
- 删除 map 元素
- map 的长度
- map 的遍历
- map 的 make
- map 的字面量初始化
- map 的 comma ok 用法
- map 的 for range 循环
- map 的 maps 包

## 10. 结构体-指针
Go 语言的结构体用于存储多个不同类型的数据。
结构体的几种形式：
- 声明结构体
- 初始化结构体
- 访问结构体元素
- 修改结构体元素
- 结构体的指针
- 结构体的匿名字段
- 结构体的嵌套
- 结构体的方法
- 结构体的字符串方法
- 结构体的标签
