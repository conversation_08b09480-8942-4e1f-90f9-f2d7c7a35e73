package main

import (
	"bufio"
	"fmt"
	"os"
	"strings"
)

// 数据结构
type Task struct {
	ID        int    //任务的唯一编号
	Content   string //任务的具体内容
	Completed bool   //任务是否已完成
}

// 新增任务
func AddTask(Content string) {
	tasks = append(tasks, Task{
		ID:        len(tasks) + 1,
		Content:   Content,
		Completed: false,
	},
	)
}

// 查看所有任务
func ListTask() {
	for _, task := range tasks {
		status := "[❌]"
		if task.Completed {
			status = "[✅]"
		}
		fmt.Println(status, task.ID, ".", task.Content)
	}
}

// 标记任务完成
func MarkAsDone(ID int) {
	for i, task := range tasks {
		if task.ID == ID {
			// fmt.Printf("TaskID: %d, index: %d\n", task.ID, i)
			tasks[i].Completed = true
		}
	}
}

// 删除任务
func DeleteTask(ID int) {
	for i, task := range tasks {
		if task.ID == ID {
			fmt.Printf("TaskID: %d, index: %d\n", task.ID, i)
			//内存效率：这种方法会创建新的底层数组，原有元素会被复制
			tasks = append(tasks[:i], tasks[i+1:]...) //... 操作符：将 slice 展开为单个元素传递给 append
		}
	}
}

// 容器存放任务列表
var tasks []Task

func main() {
	for {
		var command string
		fmt.Print("请输入以下命令执行操作>\n1.add 添加任务\n2.list 查看任务列表\n3.done 标记任务完成\n4.delete 删除任务\n5.exit 退出程序\n> ")
		fmt.Scanln(&command) //读取用户输入的一整行，存入command变量
		switch command {
		case "add":
			reader := bufio.NewReader(os.Stdin)
			fmt.Print("请输入任务内容> ")
			input, _ := reader.ReadString('\n')
			content := strings.TrimSpace(input) //去掉首尾的空白和换行符
			AddTask(content)
			fmt.Println("\033[1;32m任务添加成功\033[0m")
		case "list":
			ListTask()
		case "done":
			var doneID int
			fmt.Print("请输入要标记为完成的任务ID> ")
			fmt.Scanln(&doneID) //读取用户输入的一整行，存入doneID变量，并转换为int类型，如果输入的不是数字，则会报错
			MarkAsDone(doneID)
			fmt.Printf("\033[1;32m任务%d标记为完成\033[0m\n", doneID)
		case "delete":
			var deleteID int
			fmt.Print("请输入要删除的任务ID> ")
			fmt.Scanln(&deleteID) //读取用户输入的一整行，存入deleteID变量，并转换为int类型，如果输入的不是数字，则会报错
			DeleteTask(deleteID)
			fmt.Printf("\033[1;32m任务%d删除成功\033[0m\n", deleteID)
		case "exit":
			fmt.Println("退出程序")
			return
		default:
			fmt.Println("\033[1;31m无效的命令，请重新输入\033[0m")
		}
	}
}
